/**
 * 数据隔离测试脚本
 * 测试用户只能访问自己的对话和消息数据
 */

import { dbOperations } from './src/lib/database/index.ts';
import crypto from 'crypto';

// 创建测试用户
function createTestUsers() {
  const user1Id = crypto.randomUUID();
  const user2Id = crypto.randomUUID();

  console.log('创建测试用户:');
  console.log('用户1 ID:', user1Id);
  console.log('用户2 ID:', user2Id);

  // 在users表中创建用户记录
  try {
    dbOperations.createUser({
      id: user1Id,
      username: 'testuser1',
      email: '<EMAIL>',
      password_hash: 'dummy_hash_1'
    });

    dbOperations.createUser({
      id: user2Id,
      username: 'testuser2',
      email: '<EMAIL>',
      password_hash: 'dummy_hash_2'
    });

    console.log('✅ 测试用户已创建');
  } catch (error) {
    console.error('创建测试用户失败:', error);
    throw error;
  }

  return { user1Id, user2Id };
}

// 创建测试对话
function createTestConversations(user1Id, user2Id) {
  console.log('\n创建测试对话:');
  
  // 用户1的对话
  const conv1Id = dbOperations.createConversation({
    title: '用户1的对话1',
    model: 'llama3.2',
    user_id: user1Id
  });
  console.log('用户1对话1 ID:', conv1Id);
  
  const conv2Id = dbOperations.createConversation({
    title: '用户1的对话2',
    model: 'llama3.2',
    user_id: user1Id
  });
  console.log('用户1对话2 ID:', conv2Id);
  
  // 用户2的对话
  const conv3Id = dbOperations.createConversation({
    title: '用户2的对话1',
    model: 'llama3.2',
    user_id: user2Id
  });
  console.log('用户2对话1 ID:', conv3Id);
  
  return { conv1Id, conv2Id, conv3Id };
}

// 创建测试消息
function createTestMessages(user1Id, user2Id, conv1Id, conv2Id, conv3Id) {
  console.log('\n创建测试消息:');
  
  // 用户1对话1的消息
  const msg1Id = dbOperations.createMessage({
    conversation_id: conv1Id,
    role: 'user',
    content: '用户1在对话1中的消息',
    model: 'llama3.2',
    user_id: user1Id
  });
  console.log('用户1对话1消息 ID:', msg1Id);
  
  // 用户1对话2的消息
  const msg2Id = dbOperations.createMessage({
    conversation_id: conv2Id,
    role: 'user',
    content: '用户1在对话2中的消息',
    model: 'llama3.2',
    user_id: user1Id
  });
  console.log('用户1对话2消息 ID:', msg2Id);
  
  // 用户2对话1的消息
  const msg3Id = dbOperations.createMessage({
    conversation_id: conv3Id,
    role: 'user',
    content: '用户2在对话1中的消息',
    model: 'llama3.2',
    user_id: user2Id
  });
  console.log('用户2对话1消息 ID:', msg3Id);
  
  return { msg1Id, msg2Id, msg3Id };
}

// 测试数据隔离
function testDataIsolation(user1Id, user2Id, conv1Id, conv2Id, conv3Id) {
  console.log('\n=== 测试数据隔离 ===');
  
  // 测试1: 用户1只能看到自己的对话
  console.log('\n测试1: 用户对话隔离');
  const user1Conversations = dbOperations.getAllConversationsByUserId(user1Id);
  const user2Conversations = dbOperations.getAllConversationsByUserId(user2Id);
  
  console.log('用户1的对话数量:', user1Conversations.length);
  console.log('用户2的对话数量:', user2Conversations.length);
  
  if (user1Conversations.length === 2 && user2Conversations.length === 1) {
    console.log('✅ 对话隔离测试通过');
  } else {
    console.log('❌ 对话隔离测试失败');
  }
  
  // 测试2: 用户1不能访问用户2的对话
  console.log('\n测试2: 跨用户对话访问控制');
  const user1AccessToUser2Conv = dbOperations.getConversationByIdAndUserId(conv3Id, user1Id);
  const user2AccessToUser1Conv = dbOperations.getConversationByIdAndUserId(conv1Id, user2Id);
  
  if (!user1AccessToUser2Conv && !user2AccessToUser1Conv) {
    console.log('✅ 跨用户对话访问控制测试通过');
  } else {
    console.log('❌ 跨用户对话访问控制测试失败');
  }
  
  // 测试3: 用户只能看到自己的消息
  console.log('\n测试3: 消息隔离');
  const user1Messages = dbOperations.getMessagesByConversationIdAndUserId(conv1Id, user1Id);
  const user2Messages = dbOperations.getMessagesByConversationIdAndUserId(conv3Id, user2Id);
  const user1AccessToUser2Messages = dbOperations.getMessagesByConversationIdAndUserId(conv3Id, user1Id);
  
  console.log('用户1在自己对话中的消息数量:', user1Messages.length);
  console.log('用户2在自己对话中的消息数量:', user2Messages.length);
  console.log('用户1尝试访问用户2对话的消息数量:', user1AccessToUser2Messages.length);
  
  if (user1Messages.length === 1 && user2Messages.length === 1 && user1AccessToUser2Messages.length === 0) {
    console.log('✅ 消息隔离测试通过');
  } else {
    console.log('❌ 消息隔离测试失败');
  }
  
  // 测试4: 对话删除权限控制
  console.log('\n测试4: 对话删除权限控制');
  const deleteResult1 = dbOperations.deleteConversationByUserAndId(conv3Id, user1Id); // 用户1尝试删除用户2的对话
  const deleteResult2 = dbOperations.deleteConversationByUserAndId(conv2Id, user1Id); // 用户1删除自己的对话
  
  if (!deleteResult1 && deleteResult2) {
    console.log('✅ 对话删除权限控制测试通过');
  } else {
    console.log('❌ 对话删除权限控制测试失败');
  }
}

// 清理测试数据
function cleanup(conv1Id, conv3Id) {
  console.log('\n清理测试数据...');
  try {
    dbOperations.deleteConversation(conv1Id);
    dbOperations.deleteConversation(conv3Id);
    console.log('✅ 测试数据清理完成');
  } catch (error) {
    console.log('❌ 清理测试数据失败:', error.message);
  }
}

// 主测试函数
function runDataIsolationTest() {
  console.log('=== 开始数据隔离测试 ===');
  
  try {
    const { user1Id, user2Id } = createTestUsers();
    const { conv1Id, conv2Id, conv3Id } = createTestConversations(user1Id, user2Id);
    const { msg1Id, msg2Id, msg3Id } = createTestMessages(user1Id, user2Id, conv1Id, conv2Id, conv3Id);
    
    testDataIsolation(user1Id, user2Id, conv1Id, conv2Id, conv3Id);
    
    cleanup(conv1Id, conv3Id);
    
    console.log('\n=== 数据隔离测试完成 ===');
  } catch (error) {
    console.error('测试执行失败:', error);
  }
}

// 运行测试
runDataIsolationTest();

export { runDataIsolationTest };
